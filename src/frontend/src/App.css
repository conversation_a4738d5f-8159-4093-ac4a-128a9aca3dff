@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Poppins:wght@500;700&family=Fira+Code&display=swap');
@import '~react-markdown-editor-lite/lib/index.css';

:root {
  --primary: #ffd700; /* Blinkit yellow */
  --primary-hover: #e6c200; /* Slightly darker yellow */
  --bg: #ffffff; /* White background */
  --text: #000000; /* Black text */
  --text-light: #4a4a4a; /* Dark gray */
  --card-bg: #f8f8f8; /* Light gray background */
  --border: #e0e0e0; /* Light border */
  --accent: #00a651; /* Blinkit green */
  --success: #00a651; /* Blinkit green */
  --error: #e53935; /* Red for errors */
  --warning: #ffb300; /* Yellow for warnings */
  --tool-list-tables: #ffd700; /* Blinkit yellow */
  --tool-describe-table: #00a651; /* Blinkit green */
  --tool-execute-query: #000000; /* Black */
  --thought-bg: #f5f5f5; /* Light gray for thought bubbles */
}

/* Login Modal Styles */
.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-modal {
  background-color: var(--bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.login-modal h2 {
  color: var(--text);
  margin-bottom: 0.5rem;
  font-family: 'Poppins', sans-serif;
}

.login-modal p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.login-modal .input-group {
  display: flex;
  align-items: center;
  background-color: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
}

.login-modal .input-icon {
  color: var(--text-light);
  margin-right: 0.75rem;
}

.login-modal input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  padding: 0.5rem 0;
  outline: none;
  color: var(--text);
}

.login-modal .error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.login-modal .login-button {
  background-color: var(--primary);
  color: var(--text);
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.login-modal .login-button:hover {
  background-color: var(--primary-hover);
}

.login-modal .login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* User info styles in header */
.user-info {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  margin-right: 0.75rem;
}

.logout-button {
  background-color: transparent;
  color: var(--text-light);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-button:hover {
  background-color: var(--card-bg);
  color: var(--text);
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text);
  background-color: var(--bg);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

.app {
  display: flex;
  min-height: 100vh;
  position: relative;
  background-color: var(--bg);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  margin-left: 0;
  width: 100%;
}

.sidebar {
  width: 280px;
  background-color: #1a1a1a;
  color: #e0e0e0;
  padding: 1.5rem 1rem;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-open .sidebar {
  transform: translateX(0);
}

.sidebar-open .main-content {
  margin-left: 280px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333;
}

.sidebar-header h3 {
  margin: 0;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 500;
}

.sidebar-close {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-close:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 1.2rem;
  margin-right: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.form-group {
  margin-bottom: 0.5rem; /* Reduced from 1.25rem */
  display: flex;
  flex-direction: column;
  flex: 0 0 auto; /* Changed from flex: 1 to prevent stretching */
  min-height: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem; /* Reduced from 0.5rem */
  font-size: 0.85rem;
  color: #b3b3b3;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #333;
  background-color: #2d2d2d;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.form-control:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.selected-context {
  margin-top: 1.5rem;
  padding-top: 1.25rem;
  border-top: 1px solid #333;
}

.context-badge {
  display: inline-block;
  background-color: rgba(255, 215, 0, 0.15);
  color: var(--primary);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

/* Context Banner */
.context-banner {
  background-color: rgba(0, 166, 81, 0.1);
  color: var(--success);
  padding: 0.75rem 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(0, 166, 81, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

/* Session Info */
.session-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.session-id {
  font-size: 0.8rem;
  color: #b3b3b3;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
  letter-spacing: 0.5px;
}

/* New Chat Button */
.new-chat-button {
  background: rgba(255, 215, 0, 0.1);
  color: var(--primary);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 6px;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.new-chat-button:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
}

.new-chat-button:active {
  transform: translateY(1px);
}

/* Current Context in Header */
.current-context {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  margin-left: 0;
}

.current-context .context-icon {
  font-size: 0.9em;
  opacity: 0.8;
}

/* Loading Spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  vertical-align: middle;
  margin-left: 6px;
}

/* Tables List Container */
.tables-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 150px; /* Reduced from 200px */
  overflow: hidden;
  margin-top: 0.5rem; /* Added small top margin */
}

.tables-count {
  margin-left: 8px;
  font-size: 0.8em;
  color: var(--text-secondary);
  font-weight: normal;
}

.search-container {
  position: relative;
  margin-bottom: 0.75rem;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2rem 0.5rem 2rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--bg);
  color: var(--text);
  font-size: 0.9rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.15);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.9em;
}

.clear-search {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.2em;
  line-height: 1;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, color 0.2s;
}

.clear-search:hover {
  color: var(--text);
  background-color: var(--bg-hover);
}

.clear-search:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tables List */
.tables-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--border);
  border-radius: 6px;
  background-color: var(--bg-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
  /* Add some padding at the bottom to prevent content from being hidden behind scrollbar */
  padding-bottom: 1rem;
  /* Prevent horizontal scrollbar unless needed */
  overflow-x: hidden;
  /* Add some margin at the bottom of the scrollable area */
  margin-bottom: 0.5rem;
}

.tables-list::-webkit-scrollbar {
  width: 6px;
}

.tables-list::-webkit-scrollbar-track {
  background: transparent;
}

.tables-list::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: 3px;
}

.tables-list .table-item {
  display: flex;
  align-items: center;
  padding: 0.7rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-light);
  font-size: 0.9rem;
  color: var(--text);
}

.tables-list .table-item:last-child {
  border-bottom: none;
}

.tables-list .table-item:hover {
  background-color: var(--bg-hover);
}

.tables-list .table-item.selected {
  background-color: var(--accent-light);
  color: var(--accent);
  font-weight: 500;
}

.tables-list .table-item svg {
  margin-right: 0.75rem;
  color: var(--accent);
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.tables-list .table-item .table-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  color: #ffffff; /* White text color for table names */
}

.no-tables {
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--accent);
  animation: spin 1s ease-in-out infinite;
  margin-left: 8px;
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.search-container {
  position: relative;
  margin-bottom: 0.5rem;
}

.search-container .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #b3b3b3;
  font-size: 0.9rem;
}

.search-container .search-input {
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: var(--bg);
  color: var(--text);
}

.search-container .search-input:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.2);
}

/* Form group styles */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: #b3b3b3;
  font-weight: 500;
  color: #e0e0e0;
}

.form-control {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #333;
  background-color: #2d2d2d;
  color: #fff;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.form-control:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Context Badge */
.context-badge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 215, 0, 0.1);
  color: var(--primary);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.clear-context {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-context:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Context Hint */
.context-hint {
  color: #999;
  font-size: 0.8rem;
  text-align: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin-top: 0.5rem;
}

/* Loading Text */
.loading-text {
  color: #999;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Error Message */
.error-message {
  color: #ff6b6b;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message svg {
  flex-shrink: 0;
}

/* Sidebar Toggle */
.sidebar-toggle {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-right: 1rem;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* App Container and Layout */
.app-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg);
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: #2d3748;
  color: white;
  padding: 1rem 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: transform 0.3s ease;
  z-index: 100;
}

.sidebar-header {
  padding: 0 1rem 1rem;
  border-bottom: 1px solid #4a5568;
  margin-bottom: 1rem;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #e2e8f0;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  padding: 0 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #cbd5e0;
  text-decoration: none;
  border-radius: 0.375rem;
  margin: 0.25rem 0.5rem;
  transition: all 0.2s;
}

.nav-link:hover {
  background-color: #4a5568;
  color: white;
}

.nav-link.active {
  background-color: #4299e1;
  color: white;
}

.nav-icon {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 250px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Mobile Styles */
.mobile-header {
  display: none;
  padding: 1rem;
  background-color: #2d3748;
  color: white;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 90;
  height: 60px;
}

.mobile-header h1 {
  margin: 0;
  font-size: 1.25rem;
  margin-left: 1rem;
}

.menu-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 80;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .mobile-header {
    display: flex;
  }
  
  .main-content {
    margin-left: 0;
    padding-top: 60px;
  }
  .current-context {
    display: none;
  }
  
  .app-header h1 {
    font-size: 1.25rem;
  }
}

.context-banner strong {
  font-weight: 600;
  color: #00a651;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .sidebar {
    width: 280px;
    z-index: 1000;
  }
  
  .sidebar-open .main-content {
    margin-left: 0;
  }
  
  .app-header h1 {
    font-size: 1.25rem;
  }
}

/* Hide sidebar toggle on desktop when sidebar is open */
@media (min-width: 993px) {
  .sidebar-toggle {
    display: none;
  }
  
  .sidebar {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 280px;
  }
}

.app-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #000000;
  color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.app-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #000000;
  color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  flex: 1;
  text-align: center;
  font-family: 'Poppins', sans-serif;
  font-size: 1.75rem;
  letter-spacing: -0.025em;
}

.app-header .subtitle {
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.9;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
  padding: 1rem;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 1rem;
}

.message {
  display: flex;
  margin-bottom: 1rem;
  opacity: 1;
  max-width: 100%;
  width: 100%;
  padding: 0 1rem;
  box-sizing: border-box;
  line-height: 1.5;
  animation: fadeIn 0.3s ease;
}

.message-avatar {
  flex: 0 0 40px;
  margin-right: 1rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.avatar.assistant {
  background: linear-gradient(135deg, var(--primary), var(--accent));
}

.avatar.user {
  background: linear-gradient(135deg, var(--text), var(--text-light));
}

.message-content {
  flex: 1;
  background-color: var(--card-bg);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border);
  max-width: calc(100% - 60px);
}

.message.assistant .message-content {
  border-left: 4px solid var(--primary);
}

.message.assistant .message-text {
  background-color: var(--card-bg);
  border-top-left-radius: 0.25rem;
  color: var(--text);
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.message.user .message-content {
  background-color: var(--bg); /* Black for user messages */
  color: var(--text); /* White text */
  border-top-right-radius: 0;
  margin-left: auto;
  max-width: 85%;
}

.message.error .message-content {
  border-left: 4px solid var(--error);
  background-color: rgba(254, 226, 226, 0.4);
}

.message-text {
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.6;
}

/* Suggested Questions */
.suggested-questions {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.suggested-questions-title {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.suggested-questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.suggested-question {
  background-color: rgba(0, 166, 81, 0.1);
  color: var(--accent);
  border: 1px solid rgba(0, 166, 81, 0.2);
  border-radius: 16px;
  padding: 0.4rem 0.8rem;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  text-align: left;
}

.suggested-question:hover {
  background-color: rgba(0, 166, 81, 0.15);
  border-color: var(--accent);
}

.suggested-question:active {
  transform: translateY(1px);
}

.message-text p:first-child {
  margin-top: 0;
}

.message-text p:last-child {
  margin-bottom: 0;
}

.step {
  margin: 0.5rem 0 1rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  overflow: hidden;
}

.step[data-tool="list_tables"] {
  border-left: 3px solid var(--tool-list-tables);
}

.step[data-tool="describe_table"] {
  border-left: 3px solid var(--tool-describe-table);
}

.step[data-tool="execute_query"] {
  border-left: 3px solid var(--tool-execute-query);
}

.step[data-tool="thought"] {
  border-left: 3px solid var(--text-light);
  background-color: var(--thought-bg);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--bg); /* Black header */
  color: var(--text); /* White text */
  border-bottom: 1px solid var(--border);
  cursor: pointer;
}

.step-header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.step-toggle {
  font-size: 0.875rem;
  color: var(--text-light);
}

.step-number {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--text-light);
  margin-right: 0.5rem;
}

.step-tool {
  font-weight: 500;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  text-transform: capitalize;
}

.tool-icon {
  margin-right: 0.375rem;
}

.step[data-tool="list_tables"] .step-tool {
  color: var(--tool-list-tables);
}

.step[data-tool="describe_table"] .step-tool {
  color: var(--tool-describe-table);
}

.step[data-tool="execute_query"] .step-tool {
  color: var(--tool-execute-query);
}

.step-content {
  padding: 1rem;
  font-size: 0.875rem;
  background-color: white;
}

.step[data-tool="thought"] .step-content {
  background-color: var(--thought-bg);
}

.step-section {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.section-header {
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  color: var(--text-light);
  letter-spacing: 0.025em;
}

.step-input, .step-log, .step-observation {
  font-family: 'Fira Code', monospace;
  font-size: 0.8125rem;
  line-height: 1.5;
  overflow-x: auto;
}

.error-result, .empty-result {
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-family: 'Inter', sans-serif;
}

.error-result {
  background-color: rgba(254, 226, 226, 0.4);
  color: #b91c1c;
  border: 1px solid #fee2e2;
}

.empty-result {
  background-color: #f3f4f6;
  color: var(--text-light);
  border: 1px solid #e5e7eb;
  font-style: italic;
}

.error-log, .error-line {
  color: var(--error);
  font-style: italic;
}

.log-line {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.thinking {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.thinking-dots {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
}

.thinking-dots span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--primary);
  opacity: 0.6;
  animation: dot-flashing 1s infinite alternate;
}

.thinking-dots span:nth-child(1) {
  animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes dot-flashing {
  0% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

.input-container {
  position: sticky;
  bottom: 0;
  background-color: var(--bg);
  padding: 1rem 0;
  margin-top: 1rem;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.input-form {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: white;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.5rem;
}

.input-form input {
  flex: 1;
  border: none;
  font-size: 0.925rem;
  padding: 0.75rem;
  border-radius: 0.3125rem;
  outline: none;
  font-family: 'Inter', sans-serif;
}

.input-form input:focus {
  border-color: var(--accent); /* Green focus */
  box-shadow: 0 0 0 2px rgba(0, 166, 81, 0.2);
  outline: none;
}

.input-form button {
  background-color: var(--accent); /* Green button */
  color: white;
  border: none;
  border-radius: 0.375rem;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.input-form button:hover:not(:disabled) {
  background-color: #008c45; /* Darker green on hover */
}

.input-form button:disabled {
  background-color: var(--text-light);
  cursor: not-allowed;
  opacity: 0.7;
}

.input-hint {
  font-size: 0.75rem;
  color: var(--text-light);
  text-align: right;
  margin-top: 0.5rem;
  padding-right: 0.5rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1rem;
  text-align: center;
}

.empty-state-content {
  max-width: 32rem;
}

.empty-state h3 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: var(--primary);
  margin-top: 0;
  font-size: 1.5rem;
}

.example-questions {
  text-align: left;
  margin-top: 1.5rem;
  padding: 0;
  list-style-type: none;
}

.example-questions li {
  background-color: white;
  padding: 0.875rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.example-questions li:hover {
  background-color: rgba(0, 166, 81, 0.1); /* Light green hover */
  border-color: var(--accent);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

button.loading {
  position: relative;
}

.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-banner {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 0.75rem 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  text-align: center;
  border: 1px solid #fee2e2;
  margin: 0.5rem 1rem;
  font-weight: 500;
}

.warning-banner {
  background-color: #fffbeb;
  color: #92400e;
  padding: 0.75rem 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  text-align: center;
  border: 1px solid #fef3c7;
  margin: 0.5rem 1rem;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

/* Responsive styles */
/* Loading spinner for selects */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-left: 8px;
  display: inline-block;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 1.5rem;
  }
  .app-header .subtitle {
    font-size: 0.875rem;
  }
}

/* Thought process styling */
.thought-process-title {
  margin: 1rem 0 0.5rem;
  font-family: 'Poppins', sans-serif;
  font-size: 1.125rem;
  color: var(--primary);
  font-weight: 600;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
}

.steps-container {
  margin-top: 1rem;
  border-top: 1px solid var(--border);
  padding-top: 0.5rem;
}

.step {
  margin-bottom: 0.75rem;
  border: 1px solid var(--border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  overflow: hidden;
}

@media (max-width: 640px) {
  .message-avatar {
    flex: 0 0 32px;
    margin-right: 0.75rem;
  }
  .avatar {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
  .message-content {
    padding: 0.75rem;
  }
  .step-header {
    padding: 0.625rem 0.75rem;
  }
  .step-content {
    padding: 0.75rem;
  }
}