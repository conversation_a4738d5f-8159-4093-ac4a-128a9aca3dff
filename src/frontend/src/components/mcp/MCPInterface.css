/* MCP Interface Styles */
.mcp-interface {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

/* Loading State */
.mcp-interface.loading,
.mcp-interface.error {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.loading-message,
.error-message {
  max-width: 500px;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message {
  border-left: 4px solid #e53e3e;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2c5282;
}

.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(49, 130, 206, 0.3);
  border-radius: 50%;
  border-top-color: #3182ce;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Sidebar Styles */
.mcp-sidebar {
  width: 280px;
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
  border-right: 1px solid #4a5568;
}

.sidebar-section {
  margin-bottom: 1.5rem;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #4a5568;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #a0aec0;
}

.sidebar-icon {
  margin-right: 0.5rem;
  color: #63b3ed;
}

.server-select {
  width: 100%;
  padding: 0.5rem;
  background-color: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.server-select:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.connection-status {
  font-size: 0.8rem;
  padding: 0.5rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.connection-status.offline {
  background-color: rgba(229, 62, 62, 0.1);
  color: #fc8181;
  border-left: 3px solid #e53e3e;
}

.connection-status.online {
  background-color: rgba(72, 187, 120, 0.1);
  color: #9ae6b4;
  border-left: 3px solid #48bb78;
}

.server-select:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 1px #63b3ed;
}

.tools-list {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding-right: 0.5rem;
}

.no-tools {
  padding: 0.75rem;
  background-color: rgba(160, 174, 192, 0.1);
  border-radius: 4px;
  font-size: 0.85rem;
  color: #a0aec0;
  text-align: center;
  margin: 0.5rem 0;
}

.tool-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.tool-item:hover {
  background-color: rgba(160, 174, 192, 0.1);
}

.tool-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-item input[type="checkbox"] {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
}

.tool-name {
  font-weight: 500;
  color: #e2e8f0;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.tool-description {
  font-size: 0.85rem;
  color: #a0aec0;
  line-height: 1.4;
  flex-grow: 1;
}

.tool-config-required {
  display: block;
  font-size: 0.8rem;
  color: #f6ad55;
  margin-top: 0.25rem;
}
  margin-top: 0.25rem;
  margin-left: 1.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat Area Styles */
.mcp-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7fafc;
}

.chat-messages {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  margin: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  color: #4a5568;
}

.welcome-message h2 {
  color: #2d3748;
  margin-bottom: 1rem;
}

.message {
  display: flex;
  margin-bottom: 1.5rem;
  position: relative;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  color: #4a5568;
}

.message.user .message-avatar {
  background-color: #4299e1;
  color: white;
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
}

.message-content {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-content pre {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.75rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.message-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: #f7fafc;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
}

.typing-indicator {
  color: #a0aec0;
  font-style: italic;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Tool Call Styles */
.tool-call {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.tool-call-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f7fafc;
  cursor: pointer;
  user-select: none;
  font-weight: 500;
  color: #4a5568;
}

.tool-call-header:hover {
  background-color: #edf2f7;
}

.tool-call-icon {
  margin-right: 0.5rem;
  color: #718096;
}

.tool-call-name {
  flex: 1;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.tool-call-status {
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tool-call-status.success {
  background-color: #c6f6d5;
  color: #2f855a;
}

.tool-call-status.error {
  background-color: #fed7d7;
  color: #c53030;
}

.tool-call-details {
  padding: 0.75rem;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
}

.tool-section {
  margin-bottom: 1rem;
}

.tool-section:last-child {
  margin-bottom: 0;
}

.tool-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #718096;
  margin-bottom: 0.25rem;
}

.tool-section-content {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.8125rem;
  white-space: pre-wrap;
  background-color: #f7fafc;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  margin: 0;
}

/* Chat Input Styles */
.chat-input {
  display: flex;
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.chat-input input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  font-size: 0.95rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

.chat-input input:focus {
  border-color: #63b3ed;
  box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.2);
}

.chat-input input:disabled {
  background-color: #f7fafc;
  cursor: not-allowed;
}

.chat-input button {
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0 0.5rem;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 0.5rem;
  background-color: #3182ce;
  color: white;
}

.chat-input button:not(:disabled):hover {
  background-color: #2c5282;
}

.chat-input button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mcp-interface {
    flex-direction: column;
  }
  
  .mcp-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #4a5568;
  }
  
  .tools-list {
    max-height: 25vh;
  }
  
  .mcp-chat {
    height: calc(100vh - 40vh);
  }
}
