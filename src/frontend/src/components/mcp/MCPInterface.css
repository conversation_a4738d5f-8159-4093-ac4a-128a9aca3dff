/* MCP Interface Styles */
.mcp-interface {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Sidebar Styles */
.mcp-sidebar {
  width: 280px;
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
  border-right: 1px solid #4a5568;
}

.sidebar-section {
  margin-bottom: 1.5rem;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #4a5568;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #a0aec0;
}

.sidebar-icon {
  margin-right: 0.5rem;
  color: #63b3ed;
}

.server-select {
  width: 100%;
  padding: 0.5rem;
  background-color: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  margin-bottom: 1rem;
}

.server-select:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 1px #63b3ed;
}

.tools-list {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
}

.tool-item {
  display: block;
  padding: 0.5rem 0.25rem;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tool-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.tool-item input[type="checkbox"] {
  margin-right: 0.5rem;
}

.tool-description {
  display: block;
  font-size: 0.8rem;
  color: #a0aec0;
  margin-top: 0.25rem;
  margin-left: 1.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat Area Styles */
.mcp-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7fafc;
}

.chat-messages {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  margin: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  color: #4a5568;
}

.welcome-message h2 {
  color: #2d3748;
  margin-bottom: 1rem;
}

.message {
  display: flex;
  margin-bottom: 1.5rem;
  position: relative;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  color: #4a5568;
}

.message.user .message-avatar {
  background-color: #4299e1;
  color: white;
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
}

.message-content {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-content pre {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.75rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.message-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: #f7fafc;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
}

.typing-indicator {
  color: #a0aec0;
  font-style: italic;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Tool Call Styles */
.tool-call {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.tool-call-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f7fafc;
  cursor: pointer;
  user-select: none;
  font-weight: 500;
  color: #4a5568;
}

.tool-call-header:hover {
  background-color: #edf2f7;
}

.tool-call-icon {
  margin-right: 0.5rem;
  color: #718096;
}

.tool-call-name {
  flex: 1;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.tool-call-status {
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tool-call-status.success {
  background-color: #c6f6d5;
  color: #2f855a;
}

.tool-call-status.error {
  background-color: #fed7d7;
  color: #c53030;
}

.tool-call-details {
  padding: 0.75rem;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
}

.tool-section {
  margin-bottom: 1rem;
}

.tool-section:last-child {
  margin-bottom: 0;
}

.tool-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #718096;
  margin-bottom: 0.25rem;
}

.tool-section-content {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.8125rem;
  white-space: pre-wrap;
  background-color: #f7fafc;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  margin: 0;
}

/* Chat Input Styles */
.chat-input {
  display: flex;
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.chat-input input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.chat-input input:focus {
  border-color: #63b3ed;
  box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.2);
}

.chat-input button {
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  margin-left: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-input button:hover {
  background-color: #3182ce;
}

.chat-input button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mcp-interface {
    flex-direction: column;
  }
  
  .mcp-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #4a5568;
  }
  
  .tools-list {
    max-height: 25vh;
  }
  
  .mcp-chat {
    height: calc(100vh - 40vh);
  }
}
