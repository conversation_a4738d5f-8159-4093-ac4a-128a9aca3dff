/* MCP Interface Styles */
.mcp-interface {
  display: flex;
  height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

/* Loading State */
.mcp-interface.loading,
.mcp-interface.error {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  width: 100%;
}

.loading-message,
.error-message {
  max-width: 500px;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message {
  border-left: 4px solid #e53e3e;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2c5282;
}

.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(49, 130, 206, 0.3);
  border-radius: 50%;
  border-top-color: #3182ce;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Sidebar Styles */
.mcp-sidebar {
  width: 280px;
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
  border-right: 1px solid #4a5568;
}

.sidebar-section {
  margin-bottom: 1.5rem;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #4a5568;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #a0aec0;
}

.sidebar-icon {
  margin-right: 0.5rem;
  color: #63b3ed;
}

.server-select {
  width: 100%;
  padding: 0.5rem;
  background-color: #2d3748;
  border: 1px solid #4a5568;
  border-radius: 4px;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.server-select:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.server-select:focus {
  outline: none;
  border-color: #63b3ed;
  box-shadow: 0 0 0 1px #63b3ed;
}

.connection-status {
  font-size: 0.8rem;
  padding: 0.5rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.connection-status.offline {
  background-color: rgba(229, 62, 62, 0.1);
  color: #fc8181;
  border-left: 3px solid #e53e3e;
}

.connection-status.online {
  background-color: rgba(72, 187, 120, 0.1);
  color: #9ae6b4;
  border-left: 3px solid #48bb78;
}

/* Tools List */
.tools-list {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding-right: 0.5rem;
}

.no-tools {
  padding: 0.75rem;
  background-color: rgba(160, 174, 192, 0.1);
  border-radius: 4px;
  font-size: 0.85rem;
  color: #a0aec0;
  text-align: center;
  margin: 0.5rem 0;
}

.tool-item {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  margin: 0.25rem 0;
  border-radius: 4px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.tool-item:hover {
  background-color: rgba(160, 174, 192, 0.1);
}

.tool-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-item input[type="checkbox"] {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
}

.tool-name {
  font-weight: 500;
  color: #e2e8f0;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.tool-description {
  font-size: 0.85rem;
  color: #a0aec0;
  line-height: 1.4;
  flex-grow: 1;
}

.tool-config-required {
  display: block;
  font-size: 0.8rem;
  color: #f6ad55;
  margin-top: 0.25rem;
}

/* Chat Area */
.mcp-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7fafc;
}

.chat-messages {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  margin: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-message {
  text-align: center;
  color: #4a5568;
  padding: 2rem 1rem;
}

.welcome-message h2 {
  color: #2d3748;
  margin-bottom: 1rem;
}

.welcome-message p {
  margin-bottom: 0.5rem;
  color: #718096;
}

/* Message Styles */
.message {
  display: flex;
  margin-bottom: 1.5rem;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  color: #4a5568;
}

.message.user .message-avatar {
  margin-right: 0;
  margin-left: 1rem;
  background-color: #3182ce;
  color: white;
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
}

.message-content {
  background-color: #f7fafc;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #2d3748;
}

.message.user .message-content {
  background-color: #3182ce;
  color: white;
  border-top-right-radius: 0.25rem;
}

.message.assistant .message-content {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-top-left-radius: 0.25rem;
}

/* Typing Indicator */
.typing-indicator {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #f7fafc;
  border-radius: 1rem;
  font-size: 0.85rem;
  color: #718096;
  margin-top: 0.5rem;
  border: 1px solid #e2e8f0;
}

/* Chat Input */
.chat-input {
  display: flex;
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

.chat-input input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  font-size: 0.95rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

.chat-input input:focus {
  border-color: #63b3ed;
  box-shadow: 0 0 0 2px rgba(99, 179, 237, 0.2);
}

.chat-input input:disabled {
  background-color: #f7fafc;
  cursor: not-allowed;
}

.chat-input button {
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0 0.5rem;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 0.5rem;
  background-color: #3182ce;
  color: white;
}

.chat-input button:not(:disabled):hover {
  background-color: #2c5282;
}

.chat-input button:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
  color: #a0aec0;
}

/* Tool Call Styles */
.tool-call {
  margin-top: 0.5rem;
  border-left: 3px solid #63b3ed;
  padding-left: 0.75rem;
  margin-left: 0.5rem;
}

.tool-call-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.25rem 0;
  color: #4a5568;
  font-weight: 500;
}

.tool-call-header svg {
  margin-right: 0.5rem;
  transition: transform 0.2s;
}

.tool-call-header.expanded svg {
  transform: rotate(180deg);
}

.tool-call-content {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f7fafc;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.85rem;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mcp-interface {
    flex-direction: column;
  }

  .mcp-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #4a5568;
  }

  .tools-list {
    max-height: 30vh;
  }

  .mcp-chat {
    height: 60vh;
  }

  .message {
    max-width: 90%;
  }
}
