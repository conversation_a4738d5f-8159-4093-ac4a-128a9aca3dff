import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ser, FaChevronDown, FaChevronRight, FaServer, FaTools } from 'react-icons/fa';
import { IoSend } from 'react-icons/io5';
import ReactMarkdown from 'react-markdown';
import api from '../../api';
import './MCPInterface.css';

const ToolCall = ({ toolCall, isOpen, toggleOpen }) => {
  const { tool_name, parameters, result, error } = toolCall;
  
  return (
    <div className="tool-call">
      <div className="tool-call-header" onClick={toggleOpen}>
        <span className="tool-call-icon">
          {isOpen ? <FaChevronDown size={12} /> : <FaChevronRight size={12} />}
        </span>
        <span className="tool-call-name">{tool_name}</span>
        <span className={`tool-call-status ${error ? 'error' : 'success'}`}>
          {error ? 'Error' : 'Success'}
        </span>
      </div>
      
      {isOpen && (
        <div className="tool-call-details">
          <div className="tool-section">
            <div className="tool-section-title">Parameters</div>
            <pre className="tool-section-content">
              {JSON.stringify(parameters, null, 2)}
            </pre>
          </div>
          
          <div className="tool-section">
            <div className="tool-section-title">{error ? 'Error' : 'Result'}</div>
            <pre className="tool-section-content">
              {error ? error : JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

const MCPInterface = () => {
  const [query, setQuery] = useState('');
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [tools, setTools] = useState([]);
  const [selectedServer, setSelectedServer] = useState('');
  const [selectedTools, setSelectedTools] = useState([]);
  const [expandedToolCalls, setExpandedToolCalls] = useState({});
  const messagesEndRef = useRef(null);

  // Load available servers and tools on component mount
  useEffect(() => {
    const loadServers = async () => {
      try {
        const response = await api.get('/api/mcp/servers');
        setServers(response.data);
        if (response.data.length > 0) {
          setSelectedServer(response.data[0].id);
        }
      } catch (error) {
        console.error('Error loading servers:', error);
      }
    };

    loadServers();
  }, []);

  // Load tools when server is selected
  useEffect(() => {
    const loadTools = async () => {
      if (!selectedServer) return;
      
      try {
        const response = await api.get(`/api/mcp/servers/${selectedServer}/tools`);
        setTools(response.data);
        setSelectedTools(response.data.map(tool => tool.name)); // Select all tools by default
      } catch (error) {
        console.error('Error loading tools:', error);
      }
    };

    loadTools();
  }, [selectedServer]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const toggleToolCall = (index) => {
    setExpandedToolCalls(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!query.trim() || isLoading) return;

    const userMessage = { role: 'user', content: query };
    setMessages(prev => [...prev, userMessage]);
    setQuery('');
    setIsLoading(true);

    try {
      const response = await api.post('/api/mcp/query', {
        query,
        server_id: selectedServer,
        tool_names: selectedTools
      }, {
        responseType: 'stream',
        onDownloadProgress: (progressEvent) => {
          const lines = progressEvent.currentTarget.responseText.split('\n');
          const lastLine = lines[lines.length - 2]; // Get the last complete line
          
          try {
            const data = JSON.parse(lastLine);
            
            if (data.event === 'tool_call') {
              setMessages(prev => [
                ...prev.filter(m => m.role !== 'assistant' || !m.isTyping),
                {
                  role: 'assistant',
                  content: '',
                  tool_calls: [...(prev[prev.length - 1]?.tool_calls || []), data.data],
                  isTyping: true
                }
              ]);
            } 
            else if (data.event === 'tool_result') {
              const toolResults = Array.isArray(data.data) ? data.data : [data.data];
              
              setMessages(prev => {
                const lastMessage = prev[prev.length - 1];
                const updatedToolCalls = lastMessage.tool_calls?.map((call, index) => ({
                  ...call,
                  result: toolResults[index]?.result || call.result,
                  error: toolResults[index]?.error || call.error
                })) || [];
                
                return [
                  ...prev.slice(0, -1),
                  {
                    ...lastMessage,
                    tool_calls: updatedToolCalls,
                    isTyping: true
                  }
                ];
              });
            }
            else if (data.event === 'message') {
              setMessages(prev => [
                ...prev.filter(m => m.role !== 'assistant' || !m.isTyping),
                {
                  role: 'assistant',
                  content: data.data.content || '',
                  isTyping: true
                }
              ]);
            }
          } catch (e) {
            console.error('Error parsing stream data:', e);
          }
        }
      });
    } catch (error) {
      console.error('Error querying MCP:', error);
      setMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: 'Sorry, there was an error processing your request.',
          error: true
        }
      ]);
    } finally {
      setIsLoading(false);
      
      // Mark the last assistant message as not typing
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage?.isTyping) {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              isTyping: false
            }
          ];
        }
        return prev;
      });
    }
  };

  const renderMessageContent = (message) => {
    if (message.content) {
      return (
        <div className="message-content">
          <ReactMarkdown>{message.content}</ReactMarkdown>
        </div>
      );
    }
    
    if (message.tool_calls?.length > 0) {
      return (
        <div className="tool-calls">
          {message.tool_calls.map((toolCall, index) => (
            <ToolCall
              key={index}
              toolCall={toolCall}
              isOpen={expandedToolCalls[index] || false}
              toggleOpen={() => toggleToolCall(index)}
            />
          ))}
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className="mcp-interface">
      <div className="mcp-sidebar">
        <div className="sidebar-section">
          <div className="sidebar-header">
            <FaServer className="sidebar-icon" />
            <span>MCP Servers</span>
          </div>
          <select
            className="server-select"
            value={selectedServer}
            onChange={(e) => setSelectedServer(e.target.value)}
          >
            {servers.map(server => (
              <option key={server.id} value={server.id}>
                {server.name}
              </option>
            ))}
          </select>
        </div>

        <div className="sidebar-section">
          <div className="sidebar-header">
            <FaTools className="sidebar-icon" />
            <span>Available Tools</span>
          </div>
          <div className="tools-list">
            {tools.map(tool => (
              <label key={tool.name} className="tool-item">
                <input
                  type="checkbox"
                  checked={selectedTools.includes(tool.name)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTools([...selectedTools, tool.name]);
                    } else {
                      setSelectedTools(selectedTools.filter(t => t !== tool.name));
                    }
                  }}
                />
                <span>{tool.name}</span>
                <span className="tool-description">{tool.description}</span>
              </label>
            ))}
          </div>
        </div>
      </div>

      <div className="mcp-chat">
        <div className="chat-messages">
          {messages.length === 0 ? (
            <div className="welcome-message">
              <h2>MCP Interface</h2>
              <p>Ask a question or give a command to the MCP system.</p>
              <p>Select a server and tools from the sidebar to get started.</p>
            </div>
          ) : (
            messages.map((message, index) => (
              <div key={index} className={`message ${message.role}`}>
                <div className="message-avatar">
                  {message.role === 'user' ? <FaUser /> : <FaRobot />}
                </div>
                <div className="message-content-wrapper">
                  {renderMessageContent(message)}
                  {message.isTyping && <div className="typing-indicator">Typing...</div>}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        <form onSubmit={handleSubmit} className="chat-input">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Ask a question or give a command..."
            disabled={isLoading}
          />
          <button type="submit" disabled={isLoading}>
            <IoSend />
          </button>
        </form>
      </div>
    </div>
  );
};

export default MCPInterface;
