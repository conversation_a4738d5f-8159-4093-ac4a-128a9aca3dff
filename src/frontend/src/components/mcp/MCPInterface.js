import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ser, FaChevronDown, FaChevronRight, FaServer } from 'react-icons/fa';
import { IoSend } from 'react-icons/io5';
import ReactMarkdown from 'react-markdown';
import { mcp as mcpApi } from '../../api';
import './MCPInterface.css';

const ToolCall = ({ toolCall, isOpen, toggleOpen }) => {
  const { tool_name, parameters, result, error } = toolCall;
  
  return (
    <div className="tool-call">
      <div className="tool-call-header" onClick={toggleOpen}>
        <span className="tool-call-icon">
          {isOpen ? <FaChevronDown size={12} /> : <FaChevronRight size={12} />}
        </span>
        <span className="tool-call-name">{tool_name}</span>
        <span className={`tool-call-status ${error ? 'error' : 'success'}`}>
          {error ? 'Error' : 'Success'}
        </span>
      </div>
      
      {isOpen && (
        <div className="tool-call-details">
          <div className="tool-section">
            <div className="tool-section-title">Parameters</div>
            <pre className="tool-section-content">
              {JSON.stringify(parameters, null, 2)}
            </pre>
          </div>
          
          <div className="tool-section">
            <div className="tool-section-title">{error ? 'Error' : 'Result'}</div>
            <pre className="tool-section-content">
              {error ? error : JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

const MCPInterface = () => {
  const [query, setQuery] = useState('');
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [tools, setTools] = useState([]);
  const [selectedServer, setSelectedServer] = useState('');
  const [selectedTools, setSelectedTools] = useState([]);
  const [expandedToolCalls, setExpandedToolCalls] = useState({});
  const [error, setError] = useState(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isServerConnected, setIsServerConnected] = useState(false);
  const messagesEndRef = useRef(null);

  // Load available servers and tools on component mount
  useEffect(() => {
    const loadServers = async () => {
      try {
        setIsInitializing(true);
        setError(null);
        const response = await mcpApi.listServers();
        setServers(response.data);
        if (response.data.length > 0) {
          const defaultServer = response.data[0];
          setSelectedServer(defaultServer.id);
          setIsServerConnected(true);
        } else {
          setError('No MCP servers available. Please check your configuration.');
        }
      } catch (error) {
        console.error('Error loading servers:', error);
        setError('Failed to connect to MCP server. Please check if the server is running.');
        setIsServerConnected(false);
      } finally {
        setIsInitializing(false);
      }
    };

    loadServers();
  }, []);

  // Load tools when server is selected
  useEffect(() => {
    const loadTools = async () => {
      if (!selectedServer) return;
      
      try {
        setIsLoading(true);
        setError(null);
        const response = await mcpApi.listTools(selectedServer);
        setTools(response.data);
        
        // Only select tools that are enabled by default
        const enabledTools = response.data
          .filter(tool => !tool.requires_config || tool.is_configured)
          .map(tool => tool.name);
          
        setSelectedTools(enabledTools);
        setIsServerConnected(true);
      } catch (error) {
        console.error('Error loading tools:', error);
        setError(`Failed to load tools from server: ${error.message}`);
        setIsServerConnected(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadTools();
  }, [selectedServer]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const toggleToolCall = (index) => {
    setExpandedToolCalls(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!query.trim() || isLoading || !isServerConnected) return;

    const userMessage = { role: 'user', content: query };
    setMessages(prev => [...prev, userMessage]);
    setQuery('');
    setError(null);
    setIsLoading(true);

    try {
      const response = await mcpApi.sendQuery({
        query,
        server_id: selectedServer,
        tool_names: selectedTools
      });

      // Handle the streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep the last incomplete line in the buffer

        for (const line of lines) {
          if (!line.trim()) continue;
          
          try {
            const data = JSON.parse(line);
            
            if (data.event === 'tool_call') {
              setMessages(prev => [
                ...prev.filter(m => m.role !== 'assistant' || !m.isTyping),
                {
                  role: 'assistant',
                  content: '',
                  tool_calls: [...(prev[prev.length - 1]?.tool_calls || []), data.data],
                  isTyping: true
                }
              ]);
            } 
            else if (data.event === 'tool_result') {
              const toolResults = Array.isArray(data.data) ? data.data : [data.data];
              
              setMessages(prev => {
                const lastMessage = prev[prev.length - 1];
                const updatedToolCalls = lastMessage.tool_calls?.map((call, index) => ({
                  ...call,
                  result: toolResults[index]?.result || call.result,
                  error: toolResults[index]?.error || call.error
                })) || [];
                
                return [
                  ...prev.slice(0, -1),
                  {
                    ...lastMessage,
                    tool_calls: updatedToolCalls,
                    isTyping: true
                  }
                ];
              });
            }
            else if (data.event === 'message') {
              setMessages(prev => [
                ...prev.filter(m => m.role !== 'assistant' || !m.isTyping),
                {
                  role: 'assistant',
                  content: data.data.content || '',
                  isTyping: true
                }
              ]);
            }
          } catch (e) {
            console.error('Error parsing stream data:', e, 'Line:', line);
          }
        }
      }
    } catch (error) {
      console.error('Error querying MCP:', error);
      setMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: 'Sorry, there was an error processing your request: ' + (error.message || 'Unknown error'),
          error: true
        }
      ]);
    } finally {
      setIsLoading(false);
      
      // Mark the last assistant message as not typing
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage?.isTyping) {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              isTyping: false
            }
          ];
        }
        return prev;
      });
    }
  };

  const renderMessageContent = (message) => {
    if (message.content) {
      return (
        <div className="message-content">
          <ReactMarkdown>{message.content}</ReactMarkdown>
        </div>
      );
    }
    
    if (message.tool_calls?.length > 0) {
      return (
        <div className="tool-calls">
          {message.tool_calls.map((toolCall, index) => (
            <ToolCall
              key={index}
              toolCall={toolCall}
              isOpen={expandedToolCalls[index] || false}
              toggleOpen={() => toggleToolCall(index)}
            />
          ))}
        </div>
      );
    }
    
    return null;
  };

  if (isInitializing) {
    return (
      <div className="mcp-interface loading">
        <div className="loading-message">
          <div className="spinner"></div>
          <p>Loading MCP interface...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mcp-interface error">
        <div className="error-message">
          <h3>Connection Error</h3>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mcp-interface">
      <div className="mcp-sidebar">
        <div className="sidebar-section">
          <div className="sidebar-header">
            <FaServer className="sidebar-icon" />
            <span>MCP Servers</span>
          </div>
          <select
            className="server-select"
            value={selectedServer}
            onChange={(e) => setSelectedServer(e.target.value)}
            disabled={isLoading}
          >
            {servers.map(server => (
              <option key={server.id} value={server.id}>
                {server.name} {!server.available ? '(Offline)' : ''}
              </option>
            ))}
          </select>
          {!isServerConnected && (
            <div className="connection-status offline">
              Server is offline. Please select another server.
            </div>
          )}
        </div>

        <div className="sidebar-section">
          <div className="sidebar-header">
            <FaServer className="sidebar-icon" />
            <span>Available Tools</span>
          </div>
          <div className="tools-list">
            {tools.length === 0 ? (
              <div className="no-tools">No tools available for this server.</div>
            ) : (
              tools.map(tool => (
                <label 
                  key={tool.name} 
                  className={`tool-item ${!tool.is_configured ? 'disabled' : ''}`}
                  title={!tool.is_configured ? 'This tool requires configuration' : ''}
                >
                  <input
                    type="checkbox"
                    checked={selectedTools.includes(tool.name) && tool.is_configured}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTools([...selectedTools, tool.name]);
                      } else {
                        setSelectedTools(selectedTools.filter(t => t !== tool.name));
                      }
                    }}
                    disabled={!tool.is_configured || isLoading}
                  />
                  <span className="tool-name">{tool.name}</span>
                  <span className="tool-description">
                    {tool.description}
                    {!tool.is_configured && (
                      <span className="tool-config-required"> (Configuration required)</span>
                    )}
                  </span>
                </label>
              ))
            )}
          </div>
        </div>
      </div>

      <div className="mcp-chat">
        <div className="chat-messages">
          {messages.length === 0 ? (
            <div className="welcome-message">
              <h2>MCP Interface</h2>
              <p>Ask a question or give a command to the MCP system.</p>
              <p>Select a server and tools from the sidebar to get started.</p>
            </div>
          ) : (
            messages.map((message, index) => (
              <div key={index} className={`message ${message.role}`}>
                <div className="message-avatar">
                  {message.role === 'user' ? <FaUser /> : <FaRobot />}
                </div>
                <div className="message-content-wrapper">
                  {renderMessageContent(message)}
                  {message.isTyping && <div className="typing-indicator">Typing...</div>}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        <form onSubmit={handleSubmit} className="chat-input">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Ask a question or give a command..."
            disabled={isLoading}
          />
          <button type="submit" disabled={isLoading}>
            <IoSend />
          </button>
        </form>
      </div>
    </div>
  );
};

export default MCPInterface;
