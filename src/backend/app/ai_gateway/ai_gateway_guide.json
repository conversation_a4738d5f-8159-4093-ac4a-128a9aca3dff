AI Gateway Tool Calling
Full ChatCompletion Contract

{
  "model": "string", 
  // The model to be used for the completion request (e.g., "chatgpt-4.1")
  
  "deployment": "string", 
  // The deployment name if targeting a specific model version deployment (e.g., "azure_east_us_gpt4omni_nov24_global")
  
  "messages": [
    {
      "role": 0, 
      // Role of the message sender (0: USER, 1: SYSTEM, 2: ASSISTANT, 3: DEVE<PERSON><PERSON><PERSON>, 4: TOOL)
      
      "content": [
        {
          "type": 0, 
          // Type of the content (0: TEXT, 1: IMAGE, 2: FILE, 3: VIDEO, 4: AUDIO, 5: TOOL_USE)
          // TOOL_USE refers to a tool invocation within the conversation.
          
          "data": "string", 
          // The content of the message (e.g., plain text for text messages, base64 for image, file, audio, etc.)
          
          "mime_type": "string", 
          // MIME type for non-text content. (e.g., "image/png" for images, "application/pdf" for files)
          
          "tool_uses": [
            {
              "id": "string", 
              // Unique ID for this specific tool use instance (e.g., "tool-001")
              
              "type": 1, 
              // Type of the tool being used (1: FUNCTION - function call for invoking a tool)
              
              "function": {
                "name": "string", 
                // Name of the function being invoked (e.g., "sendMessage", "uploadFile")
                
                "description": "string", 
                // Description of what the function does (e.g., "Send a message to the user")
                
                "parameters": {
                  "recipient": "string", 
                  // Parameters required by the function (e.g., recipient of the message)
                  
                  "message": "string" 
                  // Another parameter example, message content (e.g., "Hello, World!")
                }
              }
            }
          ] 
          // List of tool uses within the content, these would be function invocations by the assistant.
        }
      ],
      
      "index": 0
    }
  ],
  
  "config": {
    "temperature": 0.7, 
    "max_tokens": 256, 
    "max_completion_tokens": 128, 
    "top_p": 1.0, 
    "top_k": 50, 
    "repetition_penalty": 1.2, 
    "presence_penalty": 0.5, 
    "stop_sequences": [
      "string"
    ],
    
    "response_format": 1, 
    // Format of the response (0: UNSPECIFIED_RESPONSE, 1: JSON_OBJECT, 2: TEXT_RESPONSE, 3: APPLICATION_JSON).
    
    "seed": 123456, 
    "safety_settings": [
      {
        "category": "HARM_CATEGORY_UNSPECIFIED", 
        "threshold": "BLOCK_LOW_AND_ABOVE", 
        "method": "SEVERITY" 
      }
    ]
  },
  
  "client_options": {
    "retry_options": {
      "max_retries": 3, 
      "initial_delay_ms": 1000, 
      "backoff_factor": 2, 
      "retry_on_codes": [500, 429] 
    },
    
    "request_options": {
      "content_type": "application/json", 
      "timeout_ms": 3000 
    },
    
    "source": "my-app" 
    // The source or name of the application that is making the request (e.g., "ml-firefly-service").
  },
  
  "tool_option": {
    "tools": [
      {
        "type": 1, 
        // Type of tool (1: FUNCTION - a function invocation tool).
        
        "function": {
          "name": "sendMessage", 
          // Name of the function to be invoked.
          
          "description": "Send a message to the user", 
          // Function description.
          
          "parameters": {
            "recipient": "user123", 
            // Parameters needed for the function (e.g., recipient user ID).
            
            "message": "Hello, world!" 
            // The message being sent to the user.
          }
        }
      }
    ],
    
    "tool_choice": {
      "mode": 1, 
      // Mode for tool selection (0: TOOL_CHOICE_MODE_UNSPECIFIED, 1: AUTO, 2: NONE, 3: FORCED).
      
      "forced_function": {
        "name": "sendMessage", 
        // Forced function when tool choice is set to "FORCED" (e.g., "sendMessage").
        
        "description": "Send a message to the user", 
        // Description of the forced function.
        
        "parameters": {
          "recipient": "user123", 
          // Parameters for the forced function (e.g., "user123" as recipient).
          
          "message": "Hello, world!" 
          // The message content.
        }
      }
    }
  },
  
  "status": {
    "code": 0, 
    // Status code (0: RESPONSE_CODE_SUCCESS, 1: FAILURE, 2: BAD_REQUEST, etc.).
    
    "message": "Request processed successfully" 
    // A message describing the status (e.g., success or error details).
  },
  
  "id": "string", 
  "created_at": 1626277630, 
  "finish_reason": "string", 
  
  "usage": {
    "prompt_tokens": 32, 
    "completion_tokens": 64, 
    "total_tokens": 96, 
    "prompt_tokens_details": {
      "cached_tokens": 10, 
      "audio_tokens": 5 
    }
  },
  
  "metadata": {
    "request_id": "abc123", 
    "latency_ms": 150 
  }
}

Enums mapping

// Role of a chat message.
enum MessageRole {
  MESSAGE_ROLE_UNSPECIFIED = 0;
  SYSTEM = 1;
  USER = 2;
  ASSISTANT = 3;
  DEVELOPER = 4;
  TOOL = 5;
}

// Type of content.
enum ContentType {
  UNSPECIFIED_CONTENT = 0;
  TEXT = 1;
  IMAGE = 2;
  FILE = 3;
  VIDEO = 4;
  AUDIO = 5;
  TOOL_USE = 6;
}

// Format used for response payload.
enum ResponseFormat {
  UNSPECIFIED_RESPONSE = 0;
  JSON_OBJECT = 1;
  TEXT_RESPONSE = 2;
  APPLICATION_JSON = 3; // For Vertex AI
}

// Tool choice mode.
enum ToolChoiceMode {
  TOOL_CHOICE_MODE_UNSPECIFIED = 0;
  AUTO = 1;
  NONE = 2;
  FORCED = 3;
}

enum ToolUseType {
  TOOL_USE_TYPE_UNSPECIFIED = 0;
  FUNCTION = 1;
}

====================
Tool Calling Examples
====================

Initial Request:
{
  "model": "gpt-4o",
  "messages": [
    {
      "role": 1,
      "content": [
        {
          "type": 1,
          "data": "You are a weather assistant."
        }
      ]
    },
    {
      "role": 2,
      "content": [
        {
          "type": 1,
          "data": "What's the weather in London?"
        }
      ]
    }
  ],
  "config": {
    "temperature": 1,
    "max_completion_tokens": 2048
    // can add more check this https://zomato.atlassian.net/wiki/spaces/MLS/pages/3827597999/Tech+Doc+AI+Gateway#RPC-Specifications
  },
  "client_options": {
    "retry_options": {
      "max_retries": 1
    },
    "request_options": {
      "content_type": "application/json",
      "timeout_ms": 10000
    },
    "source": "gw_local_testing"
  },
  "tool_option": {
    "tools": [
      {
        "type": 1,
        "function": {
          "name": "get_weather",
          "description": "Get the current weather for a city by calling an external service. You must always use this for weather queries.",
          "parameters": {
            "properties": {
              "city": {
                "description": "The city name",
                "type": "string"
              }
            },
            "required": ["city"],
            "type": "object"
          }
        }
      }
    ],
    "tool_choice": {
      "mode": 1
    }
  }
}

Initial Response:
{
  "id": "chatcmpl-BTqEJN1nGkBrkpkUwtmsUC5IcVnwp",
  "created_at": 1746451983,
  "model": "gpt-4.1-2025-04-14",
  "deployment": "openai_gpt4_1",
  "choices": [
    {
      "role": 3,
      "content": [
        {
          "type": 6,
          "tool_uses": [
            {
              "id": "call_IVAq2JLxbDd6BR84Nqa6TnzL",
              "type": 1,
              "function": {
                "name": "get_weather",
                "parameters": {
                  "city": "London"
                }
              }
            }
          ]
        }
      ]
    }
  ],
  "finish_reason": "tool_calls",
  "usage": {
    "prompt_tokens": 76,
    "completion_tokens": 15,
    "total_tokens": 91,
    "prompt_tokens_details": {}
  }
}

Follow Up Request:
{
  "model": "gpt-4o",
  "messages": [
    {
      "role": 1,
      "content": [
        {
          "type": 1,
          "data": "You are a weather assistant."
        }
      ]
    },
    {
      "role": 2,
      "content": [
        {
          "type": 1,
          "data": "What's the weather in London?"
        }
      ]
    },
    {
      "role": 3,
      "content": [
        {
          "type": 6,
          "tool_uses": [
            {
              "id": "call_IVAq2JLxbDd6BR84Nqa6TnzL",
              "type": 1,
              "function": {
                "name": "get_weather",
                "parameters": {
                  "city": "London"
                }
              }
            }
          ]
        }
      ]
    },
    {
      "role": 5,
      "content": [
        {
          "type": 6,
          "tool_uses": [
            {
              "id": "call_IVAq2JLxbDd6BR84Nqa6TnzL",
              "type": 1,
              "function": {
                "name": "get_weather",
                "result": [
                  {
                    "type": 1,
                    "data": "{\"city\":\"London\",\"temperature_celsius\":21,\"condition\":\"Cloudy\"}"
                  }
                ]
              }
            }
          ]
        }
      ]
    }
  ],
  "config": {
    "temperature": 1,
    "max_completion_tokens": 2048
  },
  "client_options": {
    "retry_options": {
      "max_retries": 1
    },
    "request_options": {
      "content_type": "application/json",
      "timeout_ms": 10000
    },
    "source": "gw_local_testing"
  },
  "tool_option": {
    "tools": [
      {
        "type": 1,
        "function": {
          "name": "get_weather",
          "description": "Get the current weather for a city by calling an external service. You must always use this for weather queries.",
          "parameters": {
            "properties": {
              "city": {
                "description": "The city name",
                "type": "string"
              }
            },
            "required": ["city"],
            "type": "object"
          }
        }
      }
    ],
    "tool_choice": {
      "mode": 1
    }
  }
}

Final Response:
{
  "id": "chatcmpl-BTqEKitjWkJl1AQaxncFFTRcJt5af",
  "created_at": 1746451984,
  "model": "gpt-4.1-2025-04-14",
  "deployment": "openai_gpt4_1",
  "choices": [
    {
      "role": 3,
      "content": [
        {
          "type": 1,
          "data": "The current weather in London is cloudy with a temperature of 21°C."
        }
      ]
    }
  ],
  "finish_reason": "stop",
  "usage": {
    "prompt_tokens": 114,
    "completion_tokens": 17,
    "total_tokens": 131,
    "prompt_tokens_details": {}
  }
}

=================
cURL Examples
=================

Initial Curl Request:
curl -L -X POST 'http://ai-gateway.eks.zdev.net/ai-gateway/api/v1/chat/completion/stream' \
  -H "Content-Type: application/json" \
  -H "Grpc-Metadata-x-project-name: dummy" \
  -H "Grpc-Metadata-x-project-auth-key: abcd" \
  -H "Keep-Alive: timeout=60" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "SYSTEM",
        "content": [
          {
            "type": "TEXT",
            "data": "You are a weather assistant."
          }
        ]
      },
      {
        "role": "USER",
        "content": [
          {
            "type": "TEXT",
            "data": "What'\''s the weather in London?"
          }
        ]
      }
    ],
    "config": {
      "temperature": 1,
      "max_completion_tokens": 2048
    },
    "client_options": {
      "source": "gw_local_testing",
      "retry_options": {
        "max_retries": 1
      },
      "request_options": {
        "content_type": "application/json",
        "timeout_ms": 10000
      }
    },
    "tool_option": {
      "tools": [
        {
          "type": "FUNCTION",
          "function": {
            "name": "get_weather",
            "description": "Get the current weather for a city by calling an external service. You must always use this for weather queries.",
            "parameters": {
              "type": "object",
              "properties": {
                "city": {
                  "type": "string",
                  "description": "The city name"
                }
              },
              "required": ["city"]
            }
          }
        }
      ],
      "tool_choice": {
        "mode": "AUTO"
      }
    }
  }'

Followup curl request:
curl -L -X POST 'http://ai-gateway.eks.zdev.net/ai-gateway/api/v1/chat/completion/stream' \
  -H "Content-Type: application/json" \
  -H "Grpc-Metadata-x-project-name: dummy" \
  -H "Grpc-Metadata-x-project-auth-key: abcd" \
  -H "Keep-Alive: timeout=60" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "SYSTEM",
        "content": [
          {
            "type": "TEXT",
            "data": "You are a weather assistant."
          }
        ]
      },
      {
        "role": "USER",
        "content": [
          {
            "type": "TEXT",
            "data": "What'\''s the weather in London?"
          }
        ]
      },
      {
        "role": "ASSISTANT",
        "content": [
          {
            "type": "TOOL_USE",
            "tool_uses": [
              {
                "id": "call_Lbs13a9uFdKyH6KuIOTx4PAt",
                "type": "FUNCTION",
                "function": {
                  "name": "get_weather",
                  "parameters": {
                    "city": "London"
                  }
                }
              }
            ]
          }
        ]
      },
      {
        "role": "TOOL",
        "content": [
          {
            "type": "TOOL_USE",
            "tool_uses": [
              {
                "id": "call_Lbs13a9uFdKyH6KuIOTx4PAt",
                "type": "FUNCTION",
                "function": {
                  "name": "get_weather",
                  "result": [
                    {
                      "type": "TEXT",
                      "data": "{\"city\":\"London\",\"temperature_celsius\":21,\"condition\":\"Cloudy\"}"
                    }
                  ]
                }
              }
            ]
          }
        ]
      }
    ],
    "config": {
      "temperature": 1,
      "max_completion_tokens": 2048
    },
    "client_options": {
      "source": "gw_local_testing",
      "retry_options": {
        "max_retries": 1
      },
      "request_options": {
        "content_type": "application/json",
        "timeout_ms": 10000
      }
    },
    "tool_option": {
      "tools": [
        {
          "type": "FUNCTION",
          "function": {
            "name": "get_weather",
            "description": "Get the current weather for a city by calling an external service. You must always use this for weather queries.",
            "parameters": {
              "type": "object",
              "properties": {
                "city": {
                  "type": "string",
                  "description": "The city name"
                }
              },
              "required": ["city"]
            }
          }
        }
      ],
      "tool_choice": {
        "mode": "AUTO"
      }
    }
  }'

Curl without tools:
curl -L -X POST 'http://ai-gateway.eks.zdev.net/ai-gateway/api/v1/stream/chat/completion' \
  -H "Content-Type: application/json" \
  -H "Grpc-Metadata-x-project-name: dummy" \
  -H "Grpc-Metadata-x-project-auth-key: abcd" \
  -H "Keep-Alive: timeout=60" \
  -d '{
    "model": "claude-3-7-sonnet",
    "messages": [
      {
        "role": "SYSTEM",
        "content": [
          {
            "type": "TEXT",
            "data": "You are a weather assistant."
          }
        ]
      },
      {
        "role": "USER",
        "content": [
          {
            "type": "TEXT",
            "data": "What'\''s the weather in London?"
          }
        ]
      }
    ],
    "config": {
      "temperature": 1,
      "max_tokens": 16384
    },
    "client_options": {
      "source": "gw_local_testing",
      "retry_options": {
        "max_retries": 1
      },
      "request_options": {
        "content_type": "application/json",
        "timeout_ms": 10000
      }
    }
  }'

curl -L -X POST 'https://stitch.zomato.com/v1/inference-service/chat-completion' \
  -H "Content-Type: application/json" \
  -H "Grpc-Metadata-x-project-name: dummy" \
  -H "Grpc-Metadata-x-project-auth-key: abcd" \
  -H "Keep-Alive: timeout=60" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "SYSTEM",
        "content": [
          {
            "type": "TEXT",
            "data": "You are a weather assistant."
          }
        ]
      },
      {
        "role": "USER",
        "content": [
          {
            "type": "TEXT",
            "data": "What'\''s the weather in London?"
          }
        ]
      }
    ],
    "config": {
      "temperature": 1,
      "max_completion_tokens": 2048
    },
    "client_options": {
      "source": "gw_local_testing",
      "retry_options": {
        "max_retries": 1
      },
      "request_options": {
        "content_type": "application/json",
        "timeout_ms": 10000
      }
    }
  }'
