"""
MCP Router

This module provides API endpoints for interacting with MCP tools.
"""

import json
import logging
import os
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
import boto3
from botocore.config import Config

from ..mcp.streamable_http_client import StreamableMCPClient
from ..utils import get_logger

# Initialize Bedrock client
bedrock_runtime = boto3.client(
    service_name='bedrock-runtime',
    region_name=os.getenv('AWS_REGION', 'us-east-1'),
    config=Config(
        retries={
            'max_attempts': 5,
            'mode': 'standard'
        }
    )
)

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/mcp",
    tags=["mcp"],
    responses={404: {"description": "Not found"}},
)

# MCP Client instance
mcp_client: Optional[StreamableMCPClient] = None

# Models
class QueryRequest(BaseModel):
    """Request model for natural language queries"""
    query: str = Field(..., description="Natural language query")
    conversation_id: Optional[str] = Field(
        None, 
        description="Optional conversation ID for maintaining context"
    )

class ToolCallRequest(BaseModel):
    """Request model for direct tool calls"""
    tool_name: str = Field(..., description="Name of the tool to call")
    parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Parameters to pass to the tool"
    )

class ToolInfo(BaseModel):
    """Information about an available tool"""
    name: str
    description: str
    input_schema: Dict[str, Any]

# Dependency to get the MCP client
async def get_mcp_client() -> StreamableMCPClient:
    """Dependency to get the MCP client instance"""
    global mcp_client
    if mcp_client is None:
        mcp_client = StreamableMCPClient()
        try:
            # Update this URL to match your MCP server
            await mcp_client.connect("http://localhost:8001/mcp")
        except Exception as e:
            raise HTTPException(
                status_code=503,
                detail="Failed to connect to MCP server. Please check if the server is running."
            )
    return mcp_client

@router.get("/tools", response_model=List[ToolInfo])
async def list_tools(client: StreamableMCPClient = Depends(get_mcp_client)):
    """List all available tools"""
    try:
        tools = await client.list_tools()
        return [
            ToolInfo(
                name=tool["name"],
                description=tool["description"],
                input_schema=tool["input_schema"]
            )
            for tool in tools
        ]
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list tools: {str(e)}"
        )

async def get_llm_response(prompt: str) -> str:
    """
    Get response from Amazon Bedrock's Claude model
    
    Args:
        prompt: The prompt to send to the LLM
        
    Returns:
        str: The raw response from the LLM in JSON format
        
    Raises:
        HTTPException: If there's an error calling the Bedrock API
    """
    try:
        # Prepare the prompt for Claude
        system_prompt = """You are a helpful assistant that helps users interact with data tools. 
        
        Your task is to analyze the user's query and determine which tool to use and with what parameters.
        
        Available tools:
        - superset_dashboard_list: List all dashboards
        - superset_chart_list: List charts, can filter by dashboard_id
        - superset_chart_get_by_id: Get details of a specific chart by ID (use chart_id parameter)
        - superset_dashboard_get_by_id: Get details of a specific dashboard by ID
        - query_database: Run a SQL query on a database
        
        Your response MUST be a valid JSON object with exactly these fields:
        {
            "tool_name": "name_of_the_tool",
            "parameters": {
                "param1": "value1"
            }
        }
        
        Examples:
        1. For "show me charts in dashboard 123":
        {
            "tool_name": "superset_chart_list",
            "parameters": {
                "dashboard_id": 123
            }
        }
        
        2. For "tell me name of chart 1987":
        {
            "tool_name": "superset_chart_get_by_id",
            "parameters": {
                "chart_id": 1987
            }
        }
        """
        
        # Format the messages for Claude
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": system_prompt},
                    {"type": "text", "text": f"User query: {prompt}"}
                ]
            }
        ]
        
        # Call Amazon Bedrock
        response = bedrock_runtime.invoke_model(
            modelId="anthropic.claude-3-sonnet-20240229-v1:0",
            body=json.dumps({
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 1000,
                "temperature": 0.1,
                "messages": messages
            })
        )
        
        # Parse the response
        response_body = json.loads(response.get('body').read())
        content = response_body.get('content', [{}])[0].get('text', '{}')
        
        # Validate that the response is valid JSON
        try:
            parsed_content = json.loads(content)
            if not isinstance(parsed_content, dict) or 'tool_name' not in parsed_content:
                raise ValueError("Invalid response format: missing 'tool_name'")
            return content
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"LLM returned invalid response: {content}. Error: {str(e)}")
            # Fallback to a default response if JSON is invalid
            return json.dumps({
                "tool_name": "superset_dashboard_list",
                "parameters": {}
            })
            
    except Exception as e:
        logger.error(f"Error calling Bedrock API: {str(e)}", exc_info=True)
        # Return a default response in case of API errors
        return json.dumps({
            "tool_name": "superset_dashboard_list",
            "parameters": {}
        })

async def determine_tool_to_use(
    query: str, 
    tools: List[Dict[str, Any]],
    conversation_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Use LLM to determine which tool to use and extract parameters
    
    Args:
        query: User's natural language query
        tools: List of available tools
        conversation_id: Optional conversation ID for context
        
    Returns:
        Dict with tool_name and parameters
    """
    try:
        # Prepare the tools description for the LLM
        tools_description = []
        for tool in tools:
            tools_description.append({
                "name": tool['name'],
                "description": tool.get('description', ''),
                "parameters": tool.get('input_schema', {}).get('properties', {})
            })
        
        # Create a prompt for the LLM
        prompt = f"""You are an AI assistant that helps users interact with data tools.
        
        Available Tools:
        {json.dumps(tools_description, indent=2)}
        
        User Query: {query}
        
        Based on the user's query, determine which tool to use and extract any parameters.
        Respond with a JSON object containing:
        - tool_name: The name of the tool to use
        - parameters: A dictionary of parameters for the tool
        
        Example Response:
        {{
            "tool_name": "tool_name_here",
            "parameters": {{
                "param1": "value1",
                "param2": "value2"
            }}
        }}
        
        Your Response:"""
        
        # Get response from LLM
        llm_response = await get_llm_response(prompt)
        
        try:
            result = json.loads(llm_response)
            # Validate the response
            if not isinstance(result, dict) or 'tool_name' not in result:
                raise ValueError("Invalid LLM response format")
                
            # Ensure parameters is a dictionary
            if 'parameters' not in result:
                result['parameters'] = {}
                
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response: {e}")
            raise ValueError("Invalid JSON response from LLM")
            
    except Exception as e:
        logger.error(f"Error in determine_tool_to_use: {str(e)}")
        raise

from fastapi.responses import StreamingResponse
from fastapi import Request
import asyncio
import uuid
from datetime import datetime

@router.post("/query")
async def process_query(
    request: QueryRequest,
    client: StreamableMCPClient = Depends(get_mcp_client)
):
    """
    Process a natural language query and return the result.
    
    This endpoint will analyze the query, determine if any tools should be called,
    and return the complete result in a single response.
    """
    try:
        # Get the list of available tools
        tools = await client.list_tools()
        
        # Determine which tool to use
        tool_to_use = await determine_tool_to_use(
            request.query, 
            tools, 
            request.conversation_id
        )
        
        # Call the tool
        result = await client.call_tool(
            tool_to_use["tool_name"],
            **tool_to_use["parameters"]
        )
        
        return {
            "status": "completed",
            "tool_used": tool_to_use["tool_name"],
            "result": result
        }
        
    except Exception as e:
        error_msg = f"Error processing query: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=400,
            detail=error_msg
        )

@router.post("/call-tool")
async def call_tool(
    request: ToolCallRequest,
    client: StreamableMCPClient = Depends(get_mcp_client)
):
    """Call a specific tool with the given parameters"""
    try:
        result = await client.call_tool(
            request.tool_name,
            **request.parameters
        )
        return {
            "tool_name": request.tool_name,
            "result": result
        }
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to call tool {request.tool_name}: {str(e)}"
        )

class ToolCall(BaseModel):
    """Model for a single tool call"""
    tool_name: str = Field(..., description="Name of the tool to call")
    parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Parameters for the tool call"
    )

class StreamRequest(BaseModel):
    """Request model for streaming API"""
    query: str = Field(..., description="The query to process")
    stream_id: Optional[str] = Field(
        None,
        description="Optional stream ID for resuming or continuing a previous stream"
    )
    conversation_id: Optional[str] = Field(
        None,
        description="Optional conversation ID for maintaining context across streams"
    )
    max_steps: int = Field(
        5,
        description="Maximum number of reasoning steps to perform",
        ge=1,
        le=10
    )

class StreamResponse(BaseModel):
    """Response model for streaming API"""
    event: str = Field(..., description="Type of event (start, thinking, tool_call, tool_result, answer, done, error)")
    stream_id: str = Field(..., description="Unique ID for this stream")
    timestamp: str = Field(..., description="ISO format timestamp")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")

def extract_answer_from_context(messages: List[Dict], query: str) -> str:
    """Extract a meaningful answer from the conversation context when LLM indicates it has enough information."""

    # Look for the most recent tool result that contains relevant data
    for message in reversed(messages):
        if message.get("role") == "assistant" and "superset_chart_get_by_id" in message.get("content", ""):
            try:
                # Try to extract chart information from the tool result
                content = message.get("content", "")

                # Look for chart information patterns
                if "chart_id" in query.lower() or "chart" in query.lower():
                    # Extract chart details
                    if "dashboard_title" in content:
                        import re
                        # Try to extract key information
                        title_match = re.search(r'"dashboard_title":\s*"([^"]*)"', content)
                        dataset_match = re.search(r'"datasource_name":\s*"([^"]*)"', content)
                        description_match = re.search(r'"description":\s*"([^"]*)"', content)

                        answer_parts = []
                        if title_match:
                            answer_parts.append(f"Chart Title: {title_match.group(1)}")
                        if dataset_match:
                            answer_parts.append(f"Dataset: {dataset_match.group(1)}")
                        if description_match and description_match.group(1):
                            answer_parts.append(f"Description: {description_match.group(1)}")

                        if answer_parts:
                            return "\n".join(answer_parts)

            except Exception as e:
                logger.error(f"Error extracting answer from context: {e}")
                continue

    # Fallback to a generic response
    return "Based on the information retrieved, I can provide you with the details about the requested item."


async def get_llm_thoughts(
    query: str,
    tools: List[Dict[str, Any]],
    conversation_history: List[Dict[str, Any]] = None,
    max_steps: int = 5
) -> Dict[str, Any]:
    """Get LLM's thoughts on the next action to take"""
    # Using double curly braces to escape them in the f-string
    system_prompt = """You are a helpful assistant that helps users interact with data tools.
    Your task is to analyze the user's query and determine what actions to take next.
    
    You can either:
    1. Call a tool if you need more information
    2. Provide a final answer if you have all the information needed
    
    Available tools:
    {tools}
    
    Your response MUST be a valid JSON object with these fields:
    - "action": Either "call_tool" or "final_answer"
    - "tool_name": Name of the tool to call (if action is "call_tool")
    - "parameters": Parameters for the tool (if action is "call_tool")
    - "reasoning": Your thought process
    - "answer": The final answer (if action is "final_answer")
    
    Example 1 - Tool call:
    ```json
    {{
        "action": "call_tool",
        "tool_name": "superset_dashboard_list",
        "parameters": {{}},
        "reasoning": "I need to list all dashboards first to understand what's available."
    }}
    ```
    
    Example 2 - Final answer:
    ```json
    {{
        "action": "final_answer",
        "answer": "Here are the top 10 charts...",
        "reasoning": "I have all the information needed to answer the query."
    }}
    ```
    
    Respond with ONLY the JSON object, no other text or markdown formatting.
    """
    
    # Format the tools list first
    tools_json = json.dumps([
        {
            "name": t["name"],
            "description": t["description"],
            "input_schema": t.get("input_schema", {})
        }
        for t in tools
    ], indent=2)
    
    # Create a more explicit system prompt
    system_prompt_content = f"""You are a helpful AI assistant that helps users interact with data tools. 
    You must respond with a JSON object that has one of these two formats:
    
    1. To call a tool:
    {{
        "action": "call_tool",
        "tool_name": "name_of_tool",
        "parameters": {{"param1": "value1"}},
        "reasoning": "Brief explanation of why you're calling this tool"
    }}
    
    2. To respond to the user:
    {{
        "action": "final_answer",
        "answer": "Your response to the user",
        "reasoning": "Your reasoning for this response"
    }}
    
    Important Rules:
    - Before calling a tool, check the conversation history to avoid repeating the same action
    - If you've already called a tool with the same parameters, don't call it again
    - If you see a tool result in the history, use that information instead of calling the tool again
    - If you need to call multiple tools, do them one at a time and wait for the result
    
    Available tools:
    {tools_json}
    
    Current query: {query}
    """
    
    # Prepare messages with conversation history
    messages = []

    # Add conversation history if available
    if conversation_history:
        for msg in conversation_history:
            # Skip any system messages in history as they should be in the system prompt
            if msg["role"] != "system":
                messages.append({"role": msg["role"], "content": msg["content"]})

    # Ensure the conversation always starts with a user message
    if not messages:
        # No conversation history, start with the current query
        messages.append({"role": "user", "content": query})
    elif messages[0]["role"] != "user":
        # First message is not from user, prepend the original query
        messages.insert(0, {"role": "user", "content": query})

    # Ensure the conversation ends with a user message if needed
    if messages[-1]["role"] != "user":
        # Last message wasn't from user, add a continuation message
        messages.append({"role": "user", "content": "Based on the tool result above, what should I do next? Should I call another tool or provide a final answer to the user?"})

    # Debug logging
    logger.info(f"Messages being sent to LLM: {[{'role': msg['role'], 'content_length': len(msg['content'])} for msg in messages]}")
    
    try:
        # Prepare the request body with explicit instructions
        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 1000,
            "temperature": 0.1,
            "system": system_prompt_content,  # System prompt as top-level parameter
            "messages": messages,  # Only user and assistant messages here
            "stop_sequences": ["\n\nHuman:", "\n\nAssistant:"],
            "top_p": 0.9
        }
        
        logger.debug(f"Sending request to Bedrock: {json.dumps(request_body, indent=2, default=str)}")
        
        response = bedrock_runtime.invoke_model(
            modelId="anthropic.claude-3-sonnet-20240229-v1:0",
            body=json.dumps(request_body, default=str)
        )
        
        # Read and log the raw response
        response_body_bytes = response.get('body').read()
        print(f"\n=== RAW RESPONSE BODY ===\n{response_body_bytes.decode('utf-8')}\n=======================\n")
        response_body = json.loads(response_body_bytes)
        logger.debug(f"Raw response body: {json.dumps(response_body, indent=2, default=str)}")
        print(f"\n=== PARSED RESPONSE ===\n{json.dumps(response_body, indent=2, default=str)}\n====================\n")
        
        # Safely extract content text
        content_items = response_body.get('content', [])
        if not isinstance(content_items, list):
            # Try to handle case where content might be a direct object
            if isinstance(response_body, dict) and 'text' in response_body:
                content_items = [{'text': response_body['text']}]
            else:
                logger.error(f"Invalid content format in response: {response_body}")
                raise ValueError("Invalid response format: content is not a list")
        
        # Handle empty content by returning a default response
        if not content_items:
            logger.warning(f"Empty content in response, using default response. Full response: {response_body}")
            return {
                "action": "final_answer",
                "answer": "I'm having trouble understanding the response. Could you please rephrase your request?",
                "reasoning": "LLM returned an empty response"
            }
            
        # Get the first content item that has text
        content = None
        for i, item in enumerate(content_items):
            logger.debug(f"Checking content item {i}: {item}")
            if isinstance(item, dict) and 'text' in item:
                content = item['text']
                logger.debug(f"Found text content: {content[:200]}...")
                break
                
        if not content:
            logger.error(f"No valid text content found in items: {content_items}")
            raise ValueError("No text content found in response")
        
        # Clean the response to extract just the JSON part
        content = content.strip()
        if content.startswith('```json'):
            content = content[7:].strip()
        if content.endswith('```'):
            content = content[:-3].strip()
            
        logger.debug(f"LLM raw response: {content}")
        
        # Parse the JSON response
        try:
            result = json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {content}")
            # Try to extract JSON from the response if it's wrapped in text
            import re
            json_match = re.search(r'\{(?:[^{}]|\{(?:[^{}]*)\})*\}', content)
            if json_match:
                try:
                    result = json.loads(json_match.group(0))
                except json.JSONDecodeError:
                    # If JSON extraction fails, check if it's a natural language response indicating completion
                    if any(phrase in content.lower() for phrase in [
                        "i now have enough context",
                        "don't need to call",
                        "provide a final answer",
                        "based on the detailed information",
                        "i can now provide"
                    ]):
                        # Extract the actual answer from the conversation history
                        answer = extract_answer_from_context(messages, query)
                        return {
                            "action": "final_answer",
                            "answer": answer,
                            "reasoning": content
                        }
                    raise ValueError("Could not parse LLM response as valid JSON")
            else:
                # Check if it's a natural language response indicating completion
                if any(phrase in content.lower() for phrase in [
                    "i now have enough context",
                    "don't need to call",
                    "provide a final answer",
                    "based on the detailed information",
                    "i can now provide"
                ]):
                    # Extract the actual answer from the conversation history
                    answer = extract_answer_from_context(messages, query)
                    return {
                        "action": "final_answer",
                        "answer": answer,
                        "reasoning": content
                    }
                raise
        
        # Validate the response structure
        if not isinstance(result, dict):
            raise ValueError("Expected a JSON object in the response")
            
        if "action" not in result:
            raise ValueError("Missing 'action' field in response")
            
        if result["action"] == "call_tool" and "tool_name" not in result:
            raise ValueError("Missing 'tool_name' for tool call")
            
        if result["action"] == "final_answer" and "answer" not in result:
            raise ValueError("Missing 'answer' for final answer")
            
        return result
        
    except (json.JSONDecodeError, ValueError) as e:
        error_content = content if 'content' in locals() else 'No content available'
        logger.error(f"Failed to process LLM response: {str(e)}\nContent: {error_content}", exc_info=True)
        return {
            "action": "final_answer",
            "answer": "I'm sorry, I encountered an error processing your request. The response format was unexpected.",
            "reasoning": f"Error: {str(e)}"
        }
    except Exception as e:
        logger.error(f"Error in get_llm_thoughts: {str(e)}", exc_info=True)
        return {
            "action": "final_answer",
            "answer": "I'm sorry, I encountered an error while processing your request.",
            "reasoning": str(e)
        }

@router.post("/stream", response_model=None)
async def stream_handler(
    request: Request,
    stream_request: StreamRequest,
    client: StreamableMCPClient = Depends(get_mcp_client)
):
    """
    Handle streaming responses for natural language queries with multi-step reasoning.
    
    This endpoint provides a persistent connection for streaming responses,
    supporting multi-step reasoning and tool usage.
    """
    stream_id = stream_request.stream_id or f"stream_{uuid.uuid4().hex}"
    
    async def event_generator():
        try:
            conversation_history = []
            tools = await client.list_tools()
            remaining_steps = stream_request.max_steps
            
            # Send stream start event
            yield StreamResponse(
                event="start",
                stream_id=stream_id,
                timestamp=datetime.utcnow().isoformat(),
                data={"query": stream_request.query, "max_steps": stream_request.max_steps}
            ).model_dump_json() + "\n"
            
            current_query = stream_request.query
            
            while remaining_steps > 0:
                remaining_steps -= 1
                
                # Get LLM's thoughts on next action
                yield StreamResponse(
                    event="thinking",
                    stream_id=stream_id,
                    timestamp=datetime.utcnow().isoformat(),
                    data={"step": stream_request.max_steps - remaining_steps}
                ).model_dump_json() + "\n"
                
                llm_thoughts = await get_llm_thoughts(
                    current_query,
                    tools,
                    conversation_history,
                    remaining_steps
                )
                
                if llm_thoughts.get("action") == "final_answer":
                    # We have a final answer
                    yield StreamResponse(
                        event="answer",
                        stream_id=stream_id,
                        timestamp=datetime.utcnow().isoformat(),
                        data={
                            "answer": llm_thoughts.get("answer", ""),
                            "reasoning": llm_thoughts.get("reasoning", ""),
                            "complete": True
                        }
                    ).model_dump_json() + "\n"
                    break
                    
                elif llm_thoughts.get("action") == "call_tool":
                    # Call the tool
                    tool_name = llm_thoughts["tool_name"]
                    tool_params = llm_thoughts.get("parameters", {})
                    
                    yield StreamResponse(
                        event="tool_call",
                        stream_id=stream_id,
                        timestamp=datetime.utcnow().isoformat(),
                        data={
                            "tool": tool_name,
                            "parameters": tool_params,
                            "reasoning": llm_thoughts.get("reasoning", "")
                        }
                    ).model_dump_json() + "\n"
                    
                    try:
                        # Ensure tool parameters are JSON serializable
                        serialized_params = {}
                        for k, v in tool_params.items():
                            try:
                                # Try to serialize the parameter
                                json.dumps(v)
                                serialized_params[k] = v
                            except (TypeError, OverflowError):
                                # If not serializable, convert to string
                                serialized_params[k] = str(v)
                        
                        try:
                            # Call the tool with serialized parameters
                            tool_result = await client.call_tool(tool_name, **serialized_params)
                            
                            # Convert tool result to a serializable format if it's a CallToolResult
                            if hasattr(tool_result, 'result'):
                                tool_result_data = tool_result.result
                                if hasattr(tool_result_data, 'model_dump'):
                                    tool_result_data = tool_result_data.model_dump()
                            else:
                                tool_result_data = tool_result
                                if hasattr(tool_result_data, 'model_dump'):
                                    tool_result_data = tool_result_data.model_dump()
                            
                            # Ensure the result is JSON serializable
                            try:
                                json.dumps(tool_result_data)
                            except (TypeError, OverflowError):
                                tool_result_data = str(tool_result_data)
                            
                            # Add the tool call and result to conversation history as a single assistant message
                            conversation_history.append({
                                "role": "assistant",
                                "content": json.dumps({
                                    "action": "tool_call_complete",
                                    "tool_name": tool_name,
                                    "parameters": tool_params,
                                    "reasoning": llm_thoughts.get("reasoning", ""),
                                    "result": tool_result_data
                                })
                            })
                            
                            # Send tool result
                            yield StreamResponse(
                                event="tool_result",
                                stream_id=stream_id,
                                timestamp=datetime.utcnow().isoformat(),
                                data={
                                    "tool": tool_name,
                                    "result": tool_result_data,
                                    "remaining_steps": remaining_steps
                                }
                            ).model_dump_json() + "\n"
                            
                        except Exception as e:
                            error_msg = f"Error processing tool result: {str(e)}"
                            logger.error(error_msg, exc_info=True)
                            yield StreamResponse(
                                event="error",
                                stream_id=stream_id,
                                timestamp=datetime.utcnow().isoformat(),
                                data={
                                    "error": error_msg,
                                    "type": type(e).__name__,
                                    "tool": tool_name
                                }
                            ).model_dump_json() + "\n"
                            continue

                        
                    except Exception as e:
                        error_msg = f"Error calling tool {tool_name}: {str(e)}"
                        logger.error(error_msg, exc_info=True)
                        yield StreamResponse(
                            event="error",
                            stream_id=stream_id,
                            timestamp=datetime.utcnow().isoformat(),
                            data={
                                "error": error_msg,
                                "type": type(e).__name__,
                                "tool": tool_name
                            }
                        ).model_dump_json() + "\n"
                        # Add error to conversation history as assistant message
                        conversation_history.append({
                            "role": "assistant",
                            "content": json.dumps({
                                "action": "tool_error",
                                "tool_name": tool_name,
                                "error": str(e),
                                "type": type(e).__name__
                            })
                        })
                
                else:
                    # Unexpected response from LLM
                    error_msg = f"Unexpected action from LLM: {llm_thoughts}"
                    logger.error(error_msg)
                    yield StreamResponse(
                        event="error",
                        stream_id=stream_id,
                        timestamp=datetime.utcnow().isoformat(),
                        data={"error": "Unexpected response from the AI model"}
                    ).model_dump_json() + "\n"
                    break
            
            # Send completion event
            yield StreamResponse(
                event="done",
                stream_id=stream_id,
                timestamp=datetime.utcnow().isoformat(),
                data={"message": "Stream completed"}
            ).model_dump_json() + "\n"
            
        except asyncio.CancelledError:
            # Handle client disconnection
            logger.info(f"Client disconnected from stream {stream_id}")
        except Exception as e:
            logger.error(f"Error in stream {stream_id}: {str(e)}", exc_info=True)
            yield StreamResponse(
                event="error",
                stream_id=stream_id,
                timestamp=datetime.utcnow().isoformat(),
                data={"error": str(e), "type": type(e).__name__}
            ).model_dump_json() + "\n"
    
    # Create a custom generator that ensures proper cleanup
    async def stream_generator():
        try:
            async for chunk in event_generator():
                if chunk:  # Only yield non-empty chunks
                    yield chunk
        except asyncio.CancelledError:
            logger.info(f"Stream was cancelled: {stream_id}")
        except Exception as e:
            logger.error(f"Error in stream generator: {str(e)}", exc_info=True)
        finally:
            # Stream cleanup completed
            pass
    
    return StreamingResponse(
        content=stream_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "Content-Type": "text/event-stream",
            "Transfer-Encoding": "chunked"
        }
    )
