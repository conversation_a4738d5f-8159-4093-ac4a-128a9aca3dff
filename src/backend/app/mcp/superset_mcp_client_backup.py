import asyncio
from typing import Optional, Any, List, Dict
from contextlib import AsyncExitStack
import os

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from anthropic import Anthropic
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()  # load environment variables from .env

class MCPClient:
    def __init__(self, model_provider: str = "anthropic", model_name: Optional[str] = None):
        """Initialize MCP Client with specified model provider
        
        Args:
            model_provider: Either 'anthropic' or 'openai' (default: 'anthropic')
            model_name: Specific model name to use (optional, uses defaults if not specified)
        """
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.model_provider = model_provider.lower()
        
        # Initialize AI clients based on provider
        if self.model_provider == "anthropic":
            self.anthropic = Anthropic()
            self.openai = None
            self.model_name = model_name or "claude-3-5-sonnet-20241022"
        elif self.model_provider == "openai":
            self.anthropic = None
            self.openai = OpenAI()
            self.model_name = model_name or "gpt-4-turbo-preview"
        else:
            raise ValueError("model_provider must be either 'anthropic' or 'openai'")
            
        print(f"Initialized MCP Client with {self.model_provider} model: {self.model_name}")
    # methods will go here
    
    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )

        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()

        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        print("\nConnected to server with tools:", [tool.name for tool in tools])
        
    async def process_query(self, query: str) -> str:
        """Process a query using the selected AI model and available tools"""
        # Validate that session is initialized
        if self.session is None:
            raise RuntimeError("MCP session not initialized. Call connect_to_server() first.")
        
        print(f"DEBUG: Session state: {self.session is not None}")
        print(f"DEBUG: Using {self.model_provider} with model {self.model_name}")
        
        messages: List[Dict[str, Any]] = [
            {
                "role": "user",
                "content": query
            }
        ]

        response = await self.session.list_tools()
        available_tools = [{
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.inputSchema
        } for tool in response.tools]

        if self.model_provider == "anthropic":
            return await self._process_with_anthropic(messages, available_tools)
        else:  # openai
            return await self._process_with_openai(messages, available_tools)

    async def _process_with_anthropic(self, messages: List[Dict[str, Any]], available_tools: List[Dict[str, Any]]) -> str:
        """Process query using Anthropic Claude"""
        # Initial Claude API call
        response = self.anthropic.messages.create(
            model=self.model_name,
            max_tokens=1000,
            messages=messages,
            tools=available_tools
        )

        # Process response and handle tool calls
        final_text = []

        assistant_message_content = []
        for content in response.content:
            if content.type == 'text':
                final_text.append(content.text)
                assistant_message_content.append(content)
            elif content.type == 'tool_use':
                tool_name = content.name
                tool_args = content.input

                # Execute tool call
                result = await self.session.call_tool(tool_name, tool_args)
                final_text.append(f"[Calling tool {tool_name} with args {tool_args}]")

                assistant_message_content.append(content)
                messages.append({
                    "role": "assistant",
                    "content": assistant_message_content
                })
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "tool_result",
                            "tool_use_id": content.id,
                            "content": result.content
                        }
                    ]
                })

                # Get next response from Claude
                response = self.anthropic.messages.create(
                    model=self.model_name,
                    max_tokens=1000,
                    messages=messages,
                    tools=available_tools
                )

                final_text.append(response.content[0].text)

        return "\n".join(final_text)

    async def _process_with_openai(self, messages: List[Dict[str, Any]], available_tools: List[Dict[str, Any]]) -> str:
        """Process query using OpenAI GPT"""
        # Convert MCP tools to OpenAI function format
        openai_tools = []
        for tool in available_tools:
            openai_tools.append({
                "type": "function",
                "function": {
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool["input_schema"]
                }
            })

        # Initial OpenAI API call
        response = self.openai.chat.completions.create(
            model=self.model_name,
            max_tokens=1000,
            messages=messages,
            tools=openai_tools if openai_tools else None
        )

        # Process response and handle tool calls
        final_text = []
        message = response.choices[0].message

        if message.content:
            final_text.append(message.content)

        # Handle tool calls if present
        if message.tool_calls:
            messages.append({
                "role": "assistant",
                "content": message.content,
                "tool_calls": [
                    {
                        "id": tool_call.id,
                        "type": "function",
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    }
                    for tool_call in message.tool_calls
                ]
            })

            for tool_call in message.tool_calls:
                tool_name = tool_call.function.name
                import json
                tool_args = json.loads(tool_call.function.arguments)

                # Execute tool call
                result = await self.session.call_tool(tool_name, tool_args)
                final_text.append(f"[Calling tool {tool_name} with args {tool_args}]")

                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": str(result.content)
                })

            # Get next response from OpenAI
            response = self.openai.chat.completions.create(
                model=self.model_name,
                max_tokens=1000,
                messages=messages,
                tools=openai_tools if openai_tools else None
            )

            if response.choices[0].message.content:
                final_text.append(response.choices[0].message.content)

        return "\n".join(final_text)

    async def chat_loop(self):
        """Run an interactive chat loop"""
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")

        while True:
            try:
                query = input("\nQuery: ").strip()

                if query.lower() == 'quit':
                    break

                response = await self.process_query(query)
                print("\n" + response)

            except Exception as e:
                print(f"\nError: {str(e)}")

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

async def main():
    if len(sys.argv) < 2:
        print("Usage: python client.py <path_to_server_script> [--provider anthropic|openai] [--model MODEL_NAME]")
        print("Examples:")
        print("  python client.py server.py")
        print("  python client.py server.py --provider openai")
        print("  python client.py server.py --provider openai --model gpt-4")
        print("  python client.py server.py --provider anthropic --model claude-3-5-sonnet-20241022")
        sys.exit(1)

    server_script = sys.argv[1]
    model_provider = "anthropic"  # default
    model_name = None
    
    # Parse command line arguments
    i = 2
    while i < len(sys.argv):
        if sys.argv[i] == "--provider" and i + 1 < len(sys.argv):
            model_provider = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--model" and i + 1 < len(sys.argv):
            model_name = sys.argv[i + 1]
            i += 2
        else:
            print(f"Unknown argument: {sys.argv[i]}")
            sys.exit(1)

    client = None
    try:
        client = MCPClient(model_provider=model_provider, model_name=model_name)
        await client.connect_to_server(server_script)
        await client.chat_loop()
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        if client is not None:
            await client.cleanup()

if __name__ == "__main__":
    import sys
    asyncio.run(main())
