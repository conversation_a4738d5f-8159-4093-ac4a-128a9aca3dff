"""
Streamable HTTP Client for MCP (Model Control Protocol)

This module provides a client for connecting to MCP servers using the streamable HTTP transport.
It supports both synchronous and asynchronous tool discovery and execution.
"""

import asyncio
import json
import logging
from contextlib import AsyncExitStack
from typing import Any, Dict, List, Optional, Tuple

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StreamableMCPClient:
    """Client for interacting with MCP servers using streamable HTTP transport"""
    
    def __init__(self):
        """Initialize the MCP client"""
        self.session: Optional[ClientSession] = None
        self.streams_context = None
        self.session_context = None
        self._exit_stack = AsyncExitStack()
    
    async def connect(self, server_url: str, headers: Optional[Dict[str, str]] = None) -> None:
        """Connect to an MCP server with HTTP Streamable transport
        
        Args:
            server_url: The URL of the MCP server (e.g., "http://localhost:8123/mcp")
            headers: Optional headers to include in the connection
        """
        try:
            # Create the streamable HTTP client
            self.streams_context = streamablehttp_client(
                url=server_url,
                headers=headers or {},
            )
            
            # Get the read/write streams
            read_stream, write_stream, _ = await self._exit_stack.enter_async_context(self.streams_context)
            
            # Create and initialize the session
            self.session_context = ClientSession(read_stream, write_stream)
            self.session = await self._exit_stack.enter_async_context(self.session_context)
            
            # Initialize the session
            await self.session.initialize()
            logger.info(f"Successfully connected to MCP server at {server_url}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            await self.close()
            raise
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List all available tools from the MCP server
        
        Returns:
            List of tool descriptions
            
        Raises:
            RuntimeError: If not connected to the server
        """
        if not self.session:
            raise RuntimeError("Not connected to server. Call connect() first.")
        
        try:
            response = await self.session.list_tools()
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema,
                }
                for tool in response.tools
            ]
        except Exception as e:
            logger.error(f"Failed to list tools: {e}")
            raise
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """Call a tool on the MCP server
        
        Args:
            tool_name: Name of the tool to call
            **kwargs: Arguments to pass to the tool
            
        Returns:
            The result of the tool execution
            
        Raises:
            RuntimeError: If not connected to the server
            ValueError: If the tool call fails
        """
        if not self.session:
            raise RuntimeError("Not connected to server. Call connect() first.")
        
        try:
            # Wrap the arguments in an 'arguments' dictionary
            # as expected by the MCP protocol
            arguments = {}
            for key, value in kwargs.items():
                # Convert parameter values to the correct type if needed
                if key.endswith('_id'):
                    try:
                        value = int(value)
                    except (ValueError, TypeError):
                        pass
                arguments[key] = value
                
            logger.info(f"Calling tool '{tool_name}' with arguments: {arguments}")
            return await self.session.call_tool(tool_name, arguments=arguments)
            
        except Exception as e:
            logger.error(f"Error calling tool '{tool_name}': {e}", exc_info=True)
            raise ValueError(f"Tool call failed: {str(e)}")
    
    async def close(self) -> None:
        """Close the connection to the MCP server"""
        try:
            await self._exit_stack.aclose()
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            self.session = None
            self.streams_context = None
            self.session_context = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


async def example_usage():
    """Example usage of the StreamableMCPClient"""
    client = StreamableMCPClient()
    
    try:
        # Connect to the MCP server
        await client.connect("http://localhost:8001/mcp")
        
        # List available tools
        tools = await client.list_tools()
        print("\n=== Available Tools ===")
        for tool in tools:
            print(f"\nTool: {tool['name']}")
            print(f"Description: {tool['description']}")
            print(f"Input Schema: {json.dumps(tool['input_schema'], indent=2)}")
        
        # Example: Call a tool if available
        if tools:
            tool_name = tools[0]['name']
            print(f"\n=== Calling tool: {tool_name} ===")
            
            try:
                # Prepare parameters based on the tool's input schema
                params = {}
                if tool['input_schema'].get('properties'):
                    for param_name, param_schema in tool['input_schema']['properties'].items():
                        # Provide a default value based on the parameter type
                        param_type = param_schema.get('type', 'string')
                        if param_type == 'number':
                            params[param_name] = 0
                        elif param_type == 'boolean':
                            params[param_name] = True
                        else:
                            params[param_name] = f"sample_{param_name}"
                
                result = await client.call_tool(tool_name, **params)
                print(f"Tool result: {json.dumps(result, indent=2)}")
                
            except Exception as e:
                print(f"Error calling tool: {e}")
    
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(example_usage())
